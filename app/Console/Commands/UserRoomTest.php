<?php

namespace App\Console\Commands;

use App\Models\goods;
use App\Models\ParcelDescriptionContainer;
use Illuminate\Console\Command;

class UserRoomTest extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:room:once';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $codes = [
            'SNX0318',
            'SNX0320',
            'SNX1348',
            'SNX06247',
            'SNX1336',
            'SNXT03082',
            'SNX06160',
            'SNX0319',
            'SNXA0312',
            'SNXA0309',
        ];

        $goodIds = goods::query()
            ->whereHas('user', function ($query) {
                return $query->where('user_room_code', 'like', '%SNXT%');
            })
            ->pluck('id')
        ;

        $filteredGoodIds = ParcelDescriptionContainer::query()
            ->whereHas('good.user', function ($query) {
                return $query->where('user_room_code', 'like', '%SNXT%');
            })
            ->pluck('good_id')
        ;

        $count = 0;
        if ($filteredGoodIds->isNotEmpty()) {
            $count = goods::query()
                ->whereIn('id', $filteredGoodIds)
                ->update(['flight_parcel_state' => 'TOOK_OUT']);
        }

        $ccount = goods::query()
            ->whereHas('user', function ($query) use($codes){
                return $query
                    ->whereIn('user_room_code', $codes)
                    ->orWhere('user_room_code', 'like', '%SNXT%')
                ;
            })
            ->update(['flight_parcel_state' => 'TOOK_OUT'])
        ;

        $this->info("case one updated: $count, case two updated: $ccount");
    }
}
