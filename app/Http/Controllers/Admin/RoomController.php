<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Room\PaymentRequest;
use App\Models\Cashflow;
use App\Models\RoomPayment;
use App\Models\TookOutScanHistory;
use App\Models\User;
use App\Service\UserService;
use Illuminate\Database\Eloquent\Relations\Concerns\CanBeOneOfMany;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use JetBrains\PhpStorm\NoReturn;

class RoomController extends Controller
{
    public function __construct(public UserService $userService)
    {
        //
    }

    public function index(Request $request)
    {
        if($request->get('user_id') == null) {
            return view('admin.room.index');
        } else {
            $request->validate([
                'user_id' => 'required',
            ]);
            if ($request->get('user_id') == null) {
                return redirect()->back()->with('error', 'შეიყვანეთ კოდი');
            }
            $user = $this->userService->clientDetail($request->get('user_id'));
            if ($user == null) {
                return redirect()->back()->with('error', 'მომხმარებელი ვერ მოიძებნა');
            }
            return view('admin.room.index', compact('user'));
        }
    }

    public function indexCourier(Request $request)
    {
        if($request->get('user_id') == null) {
            return view('admin.room.index-courier');
        } else {
            $request->validate([
                'user_id' => 'required',
            ]);
            if ($request->get('user_id') == null) {
                return redirect()->back()->with('error', 'შეიყვანეთ კოდი');
            }
            $user = $this->userService->clientDetail($request->get('user_id'));
            if ($user == null) {
                return redirect()->back()->with('error', 'მომხმარებელი ვერ მოიძებნა');
            }
            return view('admin.room.index-courier', compact('user'));
        }
    }

    public function orderTookOut(Request $request)
    {
        $request->validate([
            'tracking_code' => 'required',
            'user_id' => 'required',
        ]);


        if ($request->courier == 1)
        {
            TookOutScanHistory::query()->create([
                'tracking_code' => $request->get('tracking_code'),
                'user_id' => auth()->id(),
                'type' => 1
            ]);
        }else{
            TookOutScanHistory::query()->create([
                'tracking_code' => $request->get('tracking_code'),
                'user_id' => auth()->id(),
            ]);
        }

        $user_service = new UserService();
        $data = $user_service->orderTookOut($request->get('tracking_code'),$request->get('user_id'));

        $userData = $this->userService->clientDetail($request->get('user_id'));
        if ($data['success'])
        {
            $data['userData'] = $userData;
        }

        return response()->json($data);
    }

    public function userSearch(Request $request)
    {
      $query = $request->post('query');
        if(mb_strlen($query) <= 2) {
            return response()->json(['message' => 'აკრიფეთ მინიმუმ 2 სიმბოლო'], 400);
        }
        $users = User::query()
            ->select(['id', 'phone', 'first_name_ge', 'last_name_ge', 'branch_id', 'identification', 'user_room_code', 'balance'])
            ->with('Branch')
            ->where('user_room_code', $query)
            ->orWhere('identification', $query)
            ->orWhere(DB::raw("CONCAT(last_name_en, ' ', first_name_en)"), 'LIKE', '%' . $query . '%')
            ->orWhere(DB::raw("CONCAT(first_name_en, ' ', last_name_en)"), 'LIKE', '%' . $query . '%')
            ->orWhere(DB::raw("CONCAT(first_name_ge, ' ', last_name_ge)"), 'LIKE', '%' . $query . '%')
            ->orWhere(DB::raw("CONCAT(last_name_ge, ' ', first_name_ge)"), 'LIKE', '%' . $query . '%')
            ->orWhere('phone', $query)
            ->orderby('last_name_ge', 'asc')
            ->get();

        $response = $users->map(function ($user) {
            return [
                'id' => $user->id,
                'name' => $user->last_name_ge . ' ' . $user->first_name_ge,
                'room_code' => $user->user_room_code,
                'identification' => $user->identification,
                'phone' => $user->phone,
                'branch_title_en' => $user?->branch?->title_en
            ];
        });

        return response()->json($response);
    }

    public function payment(PaymentRequest $request): JsonResponse
    {
        $success = false;

        DB::transaction(function () use ($request, &$success) {
            $user = User::query()->where('id', $request->post('user_id'))->firstOrFail();
            $amount = abs($user->balance);

            Cashflow::create([
                "user_id" => $user->id,
                "amount" => $amount,
                "is_income" => true,
                "terminal" => true,
                "payment_source" => "terminal"
            ]);

            RoomPayment::query()->create([
                'admin_id' => auth()->id(),
                'user_id' => $user->id,
                'amount' => $amount
            ]);

            $success = User::query()
                ->where('id', $request->post('user_id'))
                ->update(['balance' => 0]);

            if (!$success) {
                throw new \Exception('Failed to update user balance');
            }
        });

        return response()->json(compact('success'));
    }

    public function paymentLog()
    {
        return view('admin.room.payment-log');
    }

    public function paymentLogData(Request $request)
    {
        $perPage = $request->input('pagination.perpage', 10);
        $keyword = $request->input('query.keyword', '');
        $page = $request->input('pagination.page', 1);

        $query = RoomPayment::query()
            ->orderByDesc('id')
            ->with(['user', 'admin'])
            ->when($keyword, function ($query, $value) {
                $query
                    ->whereHas('user', function ($query) use ($value) {
                        $query
                            ->where(DB::raw("CONCAT(first_name_ge, ' ', last_name_ge)"), 'like', "%$value%")
                            ->orWhere(DB::raw("CONCAT(last_name_ge, ' ', first_name_ge)"), 'like', "%$value%")
                            ->orWhere('phone', 'like', "%$value%")
                        ;
                    })
                    ->orWhereHas('admin', function ($query) use ($value) {
                        $query
                            ->where(DB::raw("CONCAT(first_name_ge, ' ', last_name_ge)"), 'like', "%$value%")
                            ->orWhere(DB::raw("CONCAT(last_name_ge, ' ', first_name_ge)"), 'like', "%$value%")
                            ->orWhere('phone', 'like', "%$value%")
                        ;
                    })
                    ->orWhere('amount', 'like', "%$value%")
                    ->orWhere('created_at', 'like', "%$value%");
                ;
            });


        $totalRecords = $query->count();

        $data = $query->paginate($perPage, ['*'], 'page', $page);

        $response = [
            'meta' => [
                'page' => $data->currentPage(),
                'pages' => $data->lastPage(),
                'perpage' => $data->perPage(),
                'total' => $totalRecords
            ],
            'data' => $data->items()
        ];

        return response()->json($response);

    }
}
