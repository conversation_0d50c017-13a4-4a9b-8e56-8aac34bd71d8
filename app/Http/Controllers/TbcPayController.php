<?php

namespace App\Http\Controllers;

use App\Models\Cashflow;
use App\Models\goods;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Http\Request;
use Lotuashvili\LaravelTbcPay\TbcPay;

class TbcPayController extends Controller
{
    /**
     * @var TbcPay
     */
    protected $tbc;

    /**
     * TbcPayController constructor.
     *
     * @param TbcPay $tbc
     */
    public function __construct(TbcPay $tbc)
    {
//        parent::__construct();

        $this->tbc = $tbc;
    }

    /**
     * Success handler
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function success(Request $request)
    {
        // Retrieve Transaction ID from request
        $trans_id = $request->input('trans_id');

        // Limit access to page without Transaction ID
        if (empty($trans_id)) {
            abort(404);
        }


        $transaction = Transaction::where("trans_id", $trans_id)->first();

        if (!$transaction->is_paid) {
            // Checks if transaction has been marked as paid or not
            $isOk = $this->tbc->isOk($trans_id);
            if ($isOk) {

                $user = User::find($transaction->model_id);
                $user->balance += $transaction->amount;
                $user->save();

                $cashflow = Cashflow::create([
                    "user_id" => $user->id,
                    "amount" => $transaction->amount,
                    "is_income" => true,
                    "payment_source" => "tbc"
                ]);
                $cashflow->save();


                //ჩემი დამატებული
                $user_id2 =  $user;
                //        goods that must be payed.
                $goods1 = goods::where(["user_id"=>$user_id2,"is_payed"=>0])->get();
                foreach ($goods1 as $good1) {
                    if ($user->balance>=0) {
                        $good1->is_payed = 1;
                        $good1->save();
                    }
                    else{

                    }
                }
                //ჩემი დამატებული

                return redirect('https://www.sanex.ge/user/packages');
//                return view("customer.tbc.success", compact("user", "transaction"));
                // Mark things in your database as paid
            } else {
                return view("customer.tbc.fail");
                // Display error message
            }

        } else return redirect(route("customer.redirect"));


        // Default fallback
        return redirect()->to('/');
    }

    /**
     * Fail handler
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function fail()
    {
        $this->tbc->fail();

        // Default fallback
        return redirect()->to('/');
    }
}
