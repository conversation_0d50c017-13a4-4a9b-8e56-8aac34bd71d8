<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use App\Models\goods;
use App\Models\ParcelDescription;
use App\Models\ParcelDescriptionContainer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DescriptionController extends Controller
{
    public function index(Request $request)
    {
        $query = ParcelDescription::with('branch');

        // Apply branch filter if provided
        if ($request->has('branch_id') && $request->branch_id) {
            $query->where('branch_id', $request->branch_id);
        }

        // Apply date filter if provided
        if ($request->has('date_from') && $request->date_from) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to') && $request->date_to) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $descriptions = $query->orderBy('created_at', 'desc')->paginate(15);
        $branches = Branch::all();

        return view('admin.description.index', compact('descriptions', 'branches'));
    }

    public function create()
    {
        $branches = Branch::all();
        return view('admin.description.create', compact('branches'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'branch_id' => 'required|exists:branches,id',
            'good_ids' => 'required|array',
            'good_ids.*' => 'exists:goods,id',
            'comments' => 'nullable|array',
            'comments.*' => 'nullable|string',
        ]);

        DB::beginTransaction();

        try {
            // Create description
            $description = ParcelDescription::create([
                'title' => $request->title,
                'branch_id' => $request->branch_id,
            ]);

            // Add goods to description
            foreach ($request->good_ids as $index => $goodId) {
                $container = new ParcelDescriptionContainer([
                    'parcel_description_id' => $description->id,
                    'good_id' => $goodId,
                ]);

                // Save comment if provided
                if (isset($request->comments[$index]) && !empty($request->comments[$index])) {
                    $good = goods::find($goodId);
                    if ($good) {
                        $good->small_comment = $request->comments[$index];
                        $good->save();
                    }
                }

                $container->save();
            }

            DB::commit();

            return redirect()->route('description.index')
                ->with('success', 'აღწერა წარმატებით შეიქმნა');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'შეცდომა: ' . $e->getMessage());
        }
    }

    public function edit($id)
    {
        $description = ParcelDescription::with(['branch', 'goods'])->findOrFail($id);
        $goods = $description?->goods()?->paginate(3) ?? null;

        return view('admin.description.edit', compact('description', 'goods'));
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'title' => 'required|string|max:255',
        ]);

        $description = ParcelDescription::findOrFail($id);
        $description->title = $request->title;
        $description->save();

        return redirect()->route('description.index')
            ->with('success', 'აღწერა წარმატებით განახლდა');
    }

    public function destroy($id)
    {
        $description = ParcelDescription::findOrFail($id);
        $description->delete();

        return redirect()->route('description.index')
            ->with('success', 'აღწერა წარმატებით წაიშალა');
    }

    public function getBranchParcels(Request $request)
    {
        $request->validate([
            'branch_id' => 'required|exists:branches,id',
        ]);

        $parcels = goods::with(['flight', 'Branch'])
            ->where('branch_id', $request->branch_id)
            ->where('flight_parcel_state', 'RECIEVED')
            ->get();

        return response()->json([
            'parcels' => $parcels->map(function($good) {
                return [
                    'id' => $good->id,
                    'tracking_code' => $good->tracking_code,
                    'room_number' => $good->room_number,
                    'rec_name' => $good->rec_name,
                    'flight_number' => $good->flight->flight_number ?? '',
                    'weight' => $good->phisicalw,
                    'price' => $good->price_to_pay,
                    'small_comment' => $good->small_comment,
                ];
            }),
            'total_count' => $parcels->count()
        ]);
    }

    public function scanParcel(Request $request)
    {
        $request->validate([
            'tracking_code' => 'required|string',
            'branch_id' => 'required|exists:branches,id',
        ]);

        $good = goods::with(['flight', 'Branch'])
            ->where('tracking_code', $request->tracking_code)
            ->first();

        if (!$good) {
            return response()->json([
                'custom_errors' => 'ამანათი ვერ მოიძებნა',
                'sound_type' => 'error'
            ], 404);
        }

        // Check if parcel belongs to selected branch
        if ($good->branch_id != $request->branch_id) {
            return response()->json([
                'custom_errors' => "ამანათი ეკუთვნის - " . $good->Branch->title_ge . ' ფილიალს',
                'sound_type' => 'branch_error'
            ], 400);
        }

        // Check if parcel is already picked up
        if ($good->flight_parcel_state === 'TOOK_OUT') {
            return response()->json([
                'custom_errors' => "ამანათი უკვე გატანილია",
                'sound_type' => 'already_picked_up'
            ], 400);
        }

        // Get remaining parcels count
        $remainingCount = goods::where('branch_id', $request->branch_id)
            ->where('flight_parcel_state', 'RECIEVED')
            ->where('id', '!=', $good->id)
            ->count();

        return response()->json([
            'good' => [
                'id' => $good->id,
                'tracking_code' => $good->tracking_code,
                'room_number' => $good->room_number,
                'rec_name' => $good->rec_name,
                'flight_number' => $good->flight->flight_number ?? '',
                'weight' => $good->phisicalw,
                'price' => $good->price_to_pay,
                'small_comment' => $good->small_comment,
            ],
            'remaining_count' => $remainingCount,
            'sound_type' => 'success'
        ]);
    }
}
