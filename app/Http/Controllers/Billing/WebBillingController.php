<?php

namespace App\Http\Controllers\Billing;
use App\Http\Controllers\Controller;
use App\Models\Cashflow;
use App\Models\goods;
use App\Models\TbcpayTransaction;
use App\Models\User;
use Symfony\Component\HttpKernel\Exception\HttpException;

abstract class WebBillingController extends Controller
{
    /**
     * The user model.
     *
     * @var User|null
     */
    private $user;

    /**
     * Set the user.
     *
     * @param  int  $userId
     * @return void
     */
    protected function setUser($userId)
    {
        $this->user = User::where("user_room_code",trim($userId))->first();
    }

    /**
     * Determine if user is set.
     *
     * @param  int|null  $userId
     * @return bool
     */
    protected function hasUser($userId = null)
    {
        return ! is_null($this->getUser($userId));
    }

    /**
     * Get the user.
     *
     * @param  int|null  $userId
     * @return User|null
     */
    protected function getUser($userId = null)
    {
        if ($userId) {
            $this->setUser($userId);
        }

        return $this->user;
    }

    /**
     * Get the user or fail.
     *
     * @param  int|null  $userId
     * @return User|null
     *
     * @throws \Symfony\Component\HttpKernel\Exception\HttpException
     */
    protected function getUserOrFail($userId = null)
    {
        if (! $this->hasUser($userId)) {
            throw new HttpException(404, 'User not found.');
        }

        return $this->user;
    }

    /**
     * Create the user billing transaction.
     *
     * @param  int  $transId
     * @param  float  $amount
     * @return void
     */
    protected function createTransaction($transId, $amount)
    {
        $user = $this->getUserOrFail();

        TbcpayTransaction::create([
            'user_id' => $user->id,
            'trans_id' => $transId,
            'amount' => $amount
        ]);

        //aq daumate balansi
        //ჩემი დამატებული

        $user->update(['balance' => $user->balance + $amount]);

        $cashflow = Cashflow::create([
            "user_id" => $user->id,
            "amount" => $amount,
            "is_income" => true,
            "payment_source" => "tbc"
        ]);
        $cashflow->save();

        //goods that must be payed.
        $goods = goods::where(["user_id"=>$user->id,"is_payed"=>0])->get();
        foreach ($goods as $good) {
            if ($user->balance>=0) {
                $good->is_payed = 1;
                $good->save();
            }
            else{

            }
        }
    }

    /**
     * Determine if the transaction id is duplicated.
     *
     * @param  string  $transId
     * @return bool
     */
    protected function isTransactionDuplicated( $transId)
    {
        return TbcpayTransaction::where('trans_id', $transId)->exists();
    }
}
