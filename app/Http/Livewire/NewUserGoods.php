<?php

namespace App\Http\Livewire;

use App\Models\goods;
use Livewire\Component;

class NewUserGoods extends Component
{
    public $searchTerm;
    public $foundTrackingCode;
    public $userParcelCount;
    public $good_id;
    public int $scannedRank;
    public int $goodsReportsTotal;

    public function search()
    {
        // Convert the search term to a string to avoid octal parsing
        $this->searchTerm = strval($this->searchTerm);
        // search for a matching tracking code and set $foundTrackingCode
        $this->foundTrackingCode = goods::query()->with('flight')->where('tracking_code', $this->searchTerm)->first();

        if ($this->foundTrackingCode) {
            $this->good_id = $this->foundTrackingCode->id;
            $this->foundTrackingCode->small_comment = 'SCANNED';
            $this->foundTrackingCode->sent_from_china_date = now();
            $this->foundTrackingCode->save();

            // reset the search term after a successful search
            // add a 2-second delay before resetting the search term
                $this->searchTerm = null;
        }
        else {
            // handle case where no matching Goods model is found
            $this->good_id = null;
            session()->flash('error', 'ამანათი არ მოიძებნა');
        }
        $goodsReports = goods::query()
            ->when($this->foundTrackingCode, function ($query) {
                return $query
                    ->where('flight_id', $this->foundTrackingCode->flight_id ??null)
                    ->where('user_id', $this->foundTrackingCode->user_id)
                ;
            })
            ->orderBy('id')
            ->get();

        $goodsReportsTotal = goods::query()
            ->where('user_id', $this->foundTrackingCode->user_id)
            ->where('flight_parcel_state', 'RECIEVED')
            ->count()
        ;

        $goodsReportRanks = $goodsReports->where('small_comment', 'SCANNED');
        $rank = 0;
        foreach ($goodsReportRanks as $goodsReport)
        {
            $rank ++;
            if($goodsReport->tracking_code == $this->foundTrackingCode->tracking_code)
            {
                break;
            }
        }
        $this->scannedRank = $rank;
        $this->userParcelCount = $goodsReports->count();
        $this->goodsReportsTotal = $goodsReportsTotal;


    }
    public function updatedSearchTerm()
    {
        if($this->searchTerm === null) {
            // Ignore empty input
            return;
        }

        if($this->searchTerm === "\n") {
            $this->search();
            $this->searchTerm = null;
        }
    }

    public function render()
    {
        return view('livewire.new-user-goods');
    }
}
