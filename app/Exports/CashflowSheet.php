<?php

namespace App\Exports;

use App\Models\Cashflow;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;

class CashflowSheet implements FromQuery, WithMapping, WithHeadings, WithTitle, ShouldAutoSize
{
    protected $startDate;
    protected $endDate;
    protected $branchId;

    public function __construct($startDate = null, $endDate = null, $branchId = null)
    {
        $this->startDate = $startDate;
        $this->endDate = $endDate;
        $this->branchId = $branchId;
    }

    /**
     * @return Builder
     */
    public function query()
    {
        return Cashflow::query()
            ->with(['user'])
            ->when($this->startDate, function ($query) {
                return $query->where('created_at', '>=', $this->startDate);
            })
            ->when($this->endDate, function ($query) {
                return $query->where('created_at', '<=', $this->endDate);
            })
            ->when($this->branchId, function ($query) {
                return $query->whereHas('user', function ($q) {
                    $q->where('branch_id', $this->branchId);
                });
            })
            ->orderBy('created_at', 'desc');
    }

    /**
     * @param Cashflow $cashflow
     * @return array
     */
    public function map($cashflow): array
    {
        $paymentMethod = '';

        if ($cashflow->is_income) {
            if ($cashflow->payment_source) {
                $paymentMethod = $cashflow->payment_source;
            } elseif ($cashflow->terminal) {
                $paymentMethod = 'terminal';
            } elseif (strpos($cashflow->transaction_id ?? '', 'bog') === 0) {
                $paymentMethod = 'bog';
            } elseif ($cashflow->transaction_id) {
                $paymentMethod = 'tbc';
            }
        }

        return [
            $cashflow->user->first_name_ge . ' ' . $cashflow->user->last_name_ge,
            $cashflow->user->user_room_code ?? '',
            $cashflow->invoice_id ?? '',
            $cashflow->amount,
            $cashflow->is_income ? 'შემოსავალი' : 'გასავალი',
            $paymentMethod,
            $cashflow->created_at->format('Y-m-d H:i:s'),
        ];
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'მომხმარებელის სახელი გვარი',
            'ოთახის ნომერი',
            'ინვოისის ნომერი',
            'თანხა',
            'ტიპი',
            'გადახდის წყარო',
            'გადახდის თარიღი',
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'ქეშფლოუ';
    }
}
