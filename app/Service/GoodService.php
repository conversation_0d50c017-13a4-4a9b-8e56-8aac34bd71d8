<?php

namespace App\Service;

use App\Models\Branch;
use App\Models\CanceledParcel;
use App\Models\Cashflow;
use App\Models\Flight;
use App\Models\FlightBranch;
use App\Models\goods;
use App\Models\Invoice;
use App\Models\Parameters;
use App\Models\Promotion;
use App\Models\SMSslider;
use App\Models\User;
use App\Support\SMS;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class GoodService
{
    public function deliver()
    {
        $flightId = 1112;
        $branchId = 2;
        $flight_parcel_state = 'SENT';

        $flight = Flight::query()->find($flightId);
        $flightBranch = FlightBranch::query()->where('flight_id', $flightId)->pluck('branch_id');
        $promotion = Promotion::query()->find($flight->promotion_id);
        $branches = [
                Branch::query()->where('title_ge', 'like', '%თბილისი%')->first()->id ?? null,
                Branch::query()->where('title_ge', 'like', '%ქუთაისი%')->first()->id ?? null,
                Branch::query()->where('title_ge', 'like',  '%ბათუმი%')->first()->id ?? null,
                Branch::query()->where('title_ge', 'like', '%ზუგდიდი%')->first()->id ?? null,
                Branch::query()->where('title_ge', 'like', '%რუსთავი%')->first()->id ?? null,
                Branch::query()->where('title_ge', 'like', '%გიორგი%')->first()->id ?? null,
            ];

        $sms_slider = SMSslider::query()->first();

        return goods::query()
            ->where("flight_id", $flight->id)
            ->where('branch_id', $branchId)
            ->where('flight_parcel_state', $flight_parcel_state)
            ->where('is_calculated', 0)
            ->get()
            ->groupBy('user_id')
            ->map(function ($parcel, $userId) use ($flightBranch, $flight, $promotion, $branches, $sms_slider){
                try {
                    DB::beginTransaction();
                    $smsData = [];
                    $user = User::query()->find($userId);
                    foreach ($parcel as $change_good) {
                        //$flightBranch = FlightBranch::query()->where('flight_id', $flight->id)->pluck('branch_id');

                            // update values for goods
                             $priceToPay = ($change_good->phisicalw * $flight->KG_PRICE_2) * $flight->usd_sell_rate;

                             if ($promotion)
                             {
                                 if ($promotion->weight >= $change_good->phisicalw)
                                 {
                                     $priceToPay = 0;
                                 }
                             }
                             $changeGoodUpdateData = [
                                 'flight_parcel_state' => 'RECIEVED',
                                 'price_to_pay' => $priceToPay,
                                 'is_calculated' => 1,
                             ];

                             if (!$change_good->must_customize)
                             {
                                 $smsData[$change_good->branch_id][] = $change_good->tracking_code;
                             }

                             if ($change_good->must_customize and !$change_good->customs_clearance)
                             {
                                 $smsData['undeclaredParcels'][] = $change_good->tracking_code;
                                 $changeGoodUpdateData['flight_parcel_state'] = 'SUSPENDED';
                             }
                             elseif ($flight->locked == 1 and $change_good?->is_declared == 0)
                             {
                                 unset($changeGoodUpdateData['flight_parcel_state']);
                                 CanceledParcel::query()->updateOrCreate([
                                     'good_id' => $change_good->id,
                                     'flight_id' => $flight->id
                                 ]);
                             }
                             $change_good->update($changeGoodUpdateData);

                             //dasamateblia invoice->id cashlowshi
                            $invoice = new Invoice;
                            $invoice->good_id = $change_good->id;
                            $invoice->amount = $change_good->price_to_pay;
                            //                $invoice->invoice_number =Str::uuid();
                            $invoice->save();

                            //                create new cashflow records
                            $cashflow = new Cashflow;
                            $cashflow->user_id = $change_good->user_id;
                            $cashflow->amount = -1 * abs($change_good->price_to_pay);
                            $cashflow->is_income = '0';
                            $cashflow->invoice_id = $invoice->id;
                            $cashflow->save();
                            //user is balance i cvlileba
                            $user = $user->disableBalancePermission();
                            $user->balance = $user->balance + (-1 * abs($change_good->price_to_pay));
                            $user->save();
                        }


                    if ($user->user_wants_sms == 1 and !empty($smsData)) {
                            foreach ($smsData as $branchId => $trackingCodes)
                            {
                                if ($branchId == $branches[0]) {
                                    $endOfSmsText = $sms_slider->delivered_to_tbilisi;
                                }elseif ($branchId == $branches[1]) {
                                    $endOfSmsText = $sms_slider->delivered_to_kutaisi;
                                }elseif ($branchId == $branches[2]) {
                                    $endOfSmsText = $sms_slider->delivered_to_batumi;
                                }elseif ($branchId == $branches[3]) {
                                    $endOfSmsText = $sms_slider->delivered_to_zugdidi;
                                }elseif ($branchId == $branches[4]) {
                                    $endOfSmsText = $sms_slider->delivered_to_rustavi;
                                }elseif ($branchId == $branches[5]) {
                                    $endOfSmsText = $sms_slider->delivered_to_dididighomi;
                                }elseif($branchId == 'undeclaredParcels'){
                                    $endOfSmsText = implode(', ', $trackingCodes).' შეჩერებულია, გთხოვთ განაბაჟოთ.';
                                }else{
                                    $endOfSmsText = $sms_slider->description_en;
                                }
                                $smsText = $sms_slider->title_en
//                                . PHP_EOL
//                                . implode(', ', $trackingCodes)
                                    . PHP_EOL
                                    . $endOfSmsText
                                ;
                                (new SMS($user->phone, $smsText))->send();
                            }
                        }

                    DB::commit();
                }catch (\Exception $exception){
                    Log::error('good-deliver-error', [$exception->getMessage()]);
                }
                return 1;
            })
            ->count()
        ;
    }

    public function updateStatus($id, $status, $filters, $selectAll = false): bool
    {
        // Handle the case when "select all" is chosen or when using the old 'on' array method
        if ($selectAll || (is_array($id) && count($id) === 1 && $id[0] === 'all') || in_array('on', $id))
        {
            // Apply all filters to get all matching records
            $goods = Goods::query()
                ->when($filters['custom_search'], function ($query, $value){
                    $query->where(function($q) use ($value) {
                        $q->where("rec_name", "like", "%" . $value . "%")
                          ->orWhere("tracking_code", "like", "%" . $value . "%")
                          ->orWhere("room_number", "like", "%" . $value . "%");
                    });
                })
                ->when($filters['status'], function ($query, $value){
                    $query->where('flight_parcel_state', 'like', "$value");
                })
                ->when($filters['courier'], function ($query, $value){
                    $query->where("uses_courier", (integer) ($value == 'yes'));
                })
                ->when($filters['has_admin_comment'], function ($query, $value){
                    if ($value == 'yes')
                    {
                        $query->whereNotNull('admin_comment')->where('admin_comment', '!=', '');
                    }else{
                        $query->whereNull('admin_comment')->orWhere('admin_comment', '');
                    }
                })
                ->when($filters['customer'], function ($query, $value){
                    if ($value == "SNX") {
                        $query->where('user_room_code', "like", "" . $value . "%");
                    }
                    elseif ($value == "SNXT") {
                        $query->where('user_room_code', "like", "%" . $value . "%");
                    }
                    elseif ($value == "SNXK") {
                        $query->where('user_room_code', "like", "%" . $value . "%");
                    }
                    else {
                        $query->where('user_room_code', "like", "%" . "SNX" . "%");
                    }
                })
                ->when($filters['declaration'], function ($query, $value){
                    $query->where("is_declared", (int)($value == 'yes'));
                })
                ->when($filters['customisation'], function ($query, $value){
                    $query->where("must_customize", (int)($value == 'yes'));
                })
                ->when($filters['payment'], function ($query, $value){
                    $query->where("is_payed", (int)($value == 'yes'));
                })
                ->when($filters['flight_number'], function ($query, $value){
                    $query->whereHas('flight', function ($query) use ($value) {
                        if (!empty($value)) {
                            $query->where('flight_number', "like", "%" . $value . "%");
                        }
                    });
                })
                ->get()
            ;
        }
        else
        {
            // Regular case - specific IDs selected
            $goods = Goods::query()->whereIn('id', $id)->get();
        }

        $sms_slider = SMSslider::query()->first();
        $notifiedUserIds = [];

        foreach ($goods as $good)
        {
            $old_flight_parcel_state = $good->flight_parcel_state;
            $good->update(['flight_parcel_state' => $status]);
            $user = $good->user;

            if ($good->flight_parcel_state == "RECIEVED" and $old_flight_parcel_state != "RECIEVED") {
                $branchTbilisiId = Branch::query()->where('title_ge', 'like', '%თბილისი%')->first()->id ?? null;
                $branchKutaisiId = Branch::query()->where('title_ge', 'like', '%ქუთაისი%')->first()->id ?? null;
                $branchBatumiId = Branch::query()->where('title_ge', 'like', '%ბათუმი%')->first()->id ?? null;
                $branchZugdidiId = Branch::query()->where('title_ge', 'like', '%ზუგდიდი%')->first()->id ?? null;
                $branchRustaviId = Branch::query()->where('title_ge', 'like', '%რუსთავი%')->first()->id ?? null;
                $branchDidiDighomiId = Branch::query()->where('title_ge', 'like', '%გიორგი%')->first()->id ?? null;
                $maskvaBranchId = Branch::query()->where('title_ge', 'like', '%გამზირი%')->first()->id ?? null;


                $good->price_to_pay = ($good->phisicalw * $good->flight->KG_PRICE_2) * $good->flight->usd_sell_rate;

                $good->save();
                if ($good->is_calculated == 0) {
                    // update values for goods
                    $param = Parameters::first();
                    $good->flight_parcel_state = 'RECIEVED';
                    $good->sell_rate = $good->flight->usd_sell_rate;
                    $good->goldex_kg_price = $good->flight->KG_PRICE_2;
                    $good->is_calculated = 1;
                    $good->save();

                    //dasamateblia invoice->id cashlowshi
                    $invoice = Invoice::create([
                        "good_id" => $good->id,
                        "amount" => $good->price_to_pay
                    ]);
//                $invoice->good_id = $good->id;
//                $invoice->amount = $good->price_to_pay;
                    //                $invoice->invoice_number =Str::uuid();

                    $invoice->save();

                    //                create new cashflow reccords
                    $cashflow = new Cashflow;
                    $cashflow->user_id = $good->user_id;
                    $cashflow->amount = -1 * abs($good->price_to_pay);
                    $cashflow->is_income = '0';
                    $cashflow->invoice_id = $invoice->id;
                    $cashflow->save();


                    //user is balance i cvlileba
                    $user = User::query()->where("id", $good->user_id)->first();
                    $user->balance = $user->balance + (-1 * abs($good->price_to_pay));
//                    user balances ro daematos arasworad chamochrili amanatis girebuleba dzveli wonit
//                    dd($old_price_to_pay,$good->price_to_pay);
//                    $user->balance = $user->balance + (abs($old_price_to_pay));
                    $user->save();

                    if ($user->balance > 0) {
                        $good->is_payed = 1;
                        $good->save();
                    }

                    //mail notification
//                    $user->notify(new arrivedgoods($change_good));
                    //sms
                    if ($user->user_wants_sms == 1) {
                        if(!in_array($user->id, $notifiedUserIds))
                        {
                            (new SMS($user->phone, $sms_slider->title_en . PHP_EOL . $good->tracking_code  . PHP_EOL .  $sms_slider->description_en))->send();
                        }
                    }
                }
                elseif ($good->is_calculated == 1) {
                    //        new code update invoice cashflow and user ballance
//        calculate price for goods
//                    $good->price_to_pay = ($good->phisicalw * $good->goldex_kg_price) * $good->sell_rate;
                    $good->is_calculated = 1;
//        update invoice how much to pay
                    $old_invoice = Invoice::where('good_id', $good->id)->first();
                    $old_invoice->amount = $good->price_to_pay;
                    $old_invoice->save();
//        update cashflow how much to pay
                    $cashflow = Cashflow::where('invoice_id', $old_invoice->id)->first();
                    $cashflow->user_id = $good->user_id;
                    $cashflow->amount = -1 * abs($good->price_to_pay);
                    $cashflow->is_income = '0';
                    $cashflow->save();
//         user is balance i cvlileba
                    $user = User::query()->where("id", $good->user_id)->first();
                    $user->balance = $user->balance + (-1 * abs($good->price_to_pay));
//      user balances ro daematos arasworad chamochrili amanatis girebuleba dzveli wonit
                    $user->balance = $user->balance + (abs($good->price_to_pay));
                    $user->save();
//      user balance tu dadebitia mashin amanati  chatvalos gadaxdilad.
                    if ($user->balance > 0) {
                        $good->is_payed = 1;
                        $good->save();
                    }
//                    $user->notify(new arrivedgoods($change_good));
                    //sms
                    if ($user->user_wants_sms == 1) {
                        if (!in_array($user->id, $notifiedUserIds)) {
                            (new SMS($user->phone, $sms_slider->title_en . PHP_EOL . $good->tracking_code . PHP_EOL . $sms_slider->description_en))->send();
                        }
                    }
                }
                //mail notification
//            $user->notify(new arrivedgoods($good));
                if ($user->user_wants_sms == 1) {
                    $branchId = $good->branch_id;
                    if ($branchId == $branchTbilisiId) {
                        $endOfSmsText = $sms_slider->delivered_to_tbilisi;
                    }elseif ($branchId == $branchKutaisiId) {
                        $endOfSmsText = $sms_slider->delivered_to_kutaisi;
                    }elseif ($branchId == $branchBatumiId) {
                        $endOfSmsText = $sms_slider->delivered_to_batumi;
                    }elseif ($branchId == $branchZugdidiId) {
                        $endOfSmsText = $sms_slider->delivered_to_zugdidi;
                    }elseif ($branchId == $branchRustaviId) {
                        $endOfSmsText = $sms_slider->delivered_to_rustavi;
                    }elseif ($branchId == $branchDidiDighomiId) {
                        $endOfSmsText = $sms_slider->delivered_to_dididighomi;
                    }elseif ($branchId == $maskvaBranchId) {
                        $endOfSmsText = $sms_slider->delivered_to_gamziri;
                    }else{
                        $endOfSmsText = $sms_slider->description_en;
                    }

                    if (!in_array($user->id, $notifiedUserIds)) {
                        (new SMS($user->phone, $sms_slider->title_en . PHP_EOL . $good->tracking_code . PHP_EOL . $endOfSmsText))->send();
                    }
                }

                $notifiedUserIds[] = $user->id;
            }
            if ($good->flight_parcel_state == "WAREHOUSE" and $old_flight_parcel_state != "WAREHOUSE") {
                if ($user->user_wants_sms == 1) {
                    if (!in_array($user->id, $notifiedUserIds)) {
                        $notifiedUserIds[] = $user->id;
                        (new SMS($user->phone, $sms_slider->title_ge . PHP_EOL . $good->tracking_code . PHP_EOL . $sms_slider->description_ge))->send();
                    }
                }
            }
            if ($good->flight_parcel_state == "SENT" and $old_flight_parcel_state != "SENT") {
                if ($user->user_wants_sms == 1) {
                    if (!in_array($user->id, $notifiedUserIds)) {
                        $notifiedUserIds[] = $user->id;
                        (new SMS($user->phone, $sms_slider->title_ru . PHP_EOL . $good->tracking_code  . PHP_EOL .  $sms_slider->description_ru))->send();
                    }
                }
            }
            if ($good->flight_parcel_state == "SUSPENDED" and $old_flight_parcel_state != "SUSPENDED") {
                if ($user->user_wants_sms == 1) {
                    if (!in_array($user->id, $notifiedUserIds)) {
                        $notifiedUserIds[] = $user->id;
                        (new SMS($user->phone, $sms_slider->title_es . PHP_EOL . $good->tracking_code . PHP_EOL . $sms_slider->description_es))->send();
                    }
                }
            }
            if ($good->flight_parcel_state == "RECIEVED" and $old_flight_parcel_state != "SUSPENDED"){
                $branchTbilisiId = Branch::query()->where('title_ge', 'like', '%თბილისი%')->first()->id ?? null;
                $branchKutaisiId = Branch::query()->where('title_ge', 'like', '%ქუთაისი%')->first()->id ?? null;
                $branchBatumiId  = Branch::query()->where('title_ge', 'like',  '%ბათუმი%')->first()->id ?? null;
                $branchZugdidiId = Branch::query()->where('title_ge', 'like', '%ზუგდიდი%')->first()->id ?? null;
                $branchRustaviId = Branch::query()->where('title_ge', 'like', '%რუსთავი%')->first()->id ?? null;
                $branchDidiDighomiId = Branch::query()->where('title_ge', 'like', '%გიორგი%')->first()->id ?? null;
                $maskvaBranchId = Branch::query()->where('title_ge', 'like', '%გამზირი%')->first()->id ?? null;

                $branchId = $good->branch_id;
                if ($branchId == $branchTbilisiId) {
                    $endOfSmsText = $sms_slider->delivered_to_tbilisi;
                }
                elseif ($branchId == $branchKutaisiId) {
                    $endOfSmsText = $sms_slider->delivered_to_kutaisi;
                }
                elseif ($branchId == $branchBatumiId) {
                    $endOfSmsText = $sms_slider->delivered_to_batumi;
                }
                elseif ($branchId == $branchZugdidiId) {
                    $endOfSmsText = $sms_slider->delivered_to_zugdidi;
                }
                elseif ($branchId == $branchRustaviId) {
                    $endOfSmsText = $sms_slider->delivered_to_rustavi;
                }
                elseif ($branchId == $branchDidiDighomiId) {
                    $endOfSmsText = $sms_slider->delivered_to_dididighomi;
                }
                elseif ($branchId == $maskvaBranchId) {
                    $endOfSmsText = $sms_slider->delivered_to_gamziri;
                }
                else{
                    $endOfSmsText = $sms_slider->description_en;
                }

                $smsText = $sms_slider->title_en
                    . PHP_EOL
                    . $endOfSmsText
                ;

                if (!in_array($user->id, $notifiedUserIds)) {
                    $notifiedUserIds[] = $user->id;
                    (new SMS($user->phone, $smsText))->send();
                }
            }
        }

        return $goods->isNotEmpty();
    }

    public function filter(\Illuminate\Http\Request $request, $pagination=true)
    {
        $sortColumn = $request->get('sort_column', 'id');
        $sortDirection = $request->get('sort_direction', 'asc');

        $data = goods::query()
            ->when($request->search['id_min'] ?? null, function ($query, $value){
                $query->where('id', '>=', $value);
            })
            ->when($request->search['id_max'] ?? null, function ($query, $value){
                $query->where('id', '<=', $value);
            })
            ->when($request->search['tracking_code'] ?? null, function ($query, $value){
                $query->where('tracking_code', 'like', "%$value%");
            })
            ->when($request->search['rec_name'] ?? null, function ($query, $value){
                $query->where('rec_name', 'like', "%$value%");
            })
            ->when($request->search['room_number'] ?? null, function ($query, $value){
                $query->where('room_number', 'like', "%$value%");
            })
            ->when($request->search['flight_number'] ?? null, function ($query, $value){
                $query->whereRelation('flight', 'flight_number', 'like', "%$value%");
            })
            ->when($request->search['small_comment'] ?? null, function ($query, $value){
                if ($value == 'null')
                {
                    $query->whereNull('small_comment');
                }
                else
                {
                    $query->where('small_comment', $value);
                }
            })
            ->when($request->search['start_takeout_date'] ?? null, function ($query, $value){
                $query->whereRelation('flight', 'takeout_date', '>=', $value);
            })
            ->when($request->search['end_takeout_date'] ?? null, function ($query, $value){
                $query->whereRelation('flight', 'takeout_date', '<=', $value);
            })

            ->when($request->search['phisicalw'] ?? null, function ($query, $value){
                $query->where('phisicalw', $value);
            })
            ->when($request->search['price_to_pay'] ?? null, function ($query, $value){
                $query->where('price_to_pay', $value);
            })

            ->when($request->search['flight_parcel_state'] ?? null, function ($query, $value){
                if ($value == 'NONE')
                {
                    $query->whereNull('flight_parcel_state');
                }
                else
                {
                    $query->where('flight_parcel_state', $value);
                }
            })
            ->when(!is_null($request->search['is_payed'] ?? null), function ($query) use ($request) {
                $query->where('is_payed', (int) $request->search['is_payed']);
            })
            ->when(!is_null($request->search['is_declared'] ?? null), function ($query) use ($request) {
                $query->where('is_declared', (int) $request->search['is_declared']);
            })
            ->when(!is_null($request->search['must_customize'] ?? null), function ($query) use ($request) {
                $query->where('must_customize', (int) $request->search['must_customize']);
            })
            ->when(!is_null($request->search['uses_courier'] ?? null), function ($query) use ($request) {
                $query->where('uses_courier', (int) $request->search['uses_courier']);
            })
            ->when($request->search['branch_id'] ?? null, function ($query, $value) {
                $query->where('branch_id', $value);
            })
            ->with(['flight', 'Branch'])
            ->orderBy($sortColumn, $sortDirection)
        ;


        return $pagination
            ? $data->paginate($request->get('per_page', 10))
            : $data->get()
        ;
    }
}